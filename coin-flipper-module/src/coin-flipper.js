/**
 * CoinFlipper Module for Vue.js
 * รวม Three.js, AudioManager, และ CoinRenderer เข้าด้วยกันเป็นชุดเดียว
 * สามารถ import ใช้ใน Vue.js ได้โดยตรง
 */

// Import Three.js (จะโหลดจาก CDN หรือ npm package)
let THREE;

// ตรวจสอบว่า Three.js มีอยู่แล้วหรือไม่
if (typeof window !== 'undefined' && window.THREE) {
    THREE = window.THREE;
} else if (typeof require !== 'undefined') {
    try {
        THREE = require('three');
    } catch (e) {
        console.warn('Three.js not found. Please install: npm install three');
    }
}

/**
 * AudioManager Class - จัดการเสียงทั้งหมด
 */
class AudioManager {
    constructor() {
        this.audioContext = null;
        this.initAudioContext();
    }

    initAudioContext() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        } catch (error) {
            console.warn('Web Audio API not supported');
        }
    }

    resumeAudioContext() {
        if (this.audioContext && this.audioContext.state === 'suspended') {
            this.audioContext.resume();
        }
    }

    /**
     * สร้างเสียงการทอยเหรียญที่สมจริง
     * ประกอบด้วย 3 ส่วน: เสียงเสียดสี, เสียงกระเด้ง, และเสียงหมุนช้าลง
     */
    generateFlipSound() {
        if (!this.audioContext) return;

        const now = this.audioContext.currentTime;

        // 1. เสียงเสียดสีของโลหะ (white noise + lowpass filter เพื่อความรู้สึกแบบขูด)
        this.playMetalScrape(now, 0.25);

        // 2. เสียงเหรียญกระเด้ง (แยกช่วงเวลาให้เหมือนจริง)
        const bounceTimes = [0.3, 0.7, 1.0, 1.2];  // เวลาที่เหรียญกระเด้งแต่ละครั้ง
        bounceTimes.forEach((delay, i) => {
            // ความดังลดลงในแต่ละครั้งที่กระเด้ง
            this.playMetalClick(now + delay, 1 - i * 0.15);
        });

        // 3. เสียงหมุนและหยุดช้าๆ (oscillator ความถี่ต่ำแบบโซ่)
        this.playWobbleSpin(now + 0.9, 0.6);
    }

    playMetalScrape(startTime, duration) {
        if (!this.audioContext) return;

        const bufferSize = this.audioContext.sampleRate * duration;
        const buffer = this.audioContext.createBuffer(1, bufferSize, this.audioContext.sampleRate);
        const data = buffer.getChannelData(0);

        for (let i = 0; i < bufferSize; i++) {
            data[i] = (Math.random() * 2 - 1) * (1 - i / bufferSize); // white noise fade
        }

        const noise = this.audioContext.createBufferSource();
        noise.buffer = buffer;

        const filter = this.audioContext.createBiquadFilter();
        filter.type = 'lowpass';
        filter.frequency.setValueAtTime(800, startTime);

        const gain = this.audioContext.createGain();
        gain.gain.setValueAtTime(0.3, startTime);
        gain.gain.exponentialRampToValueAtTime(0.01, startTime + duration);

        noise.connect(filter).connect(gain).connect(this.audioContext.destination);
        noise.start(startTime);
        noise.stop(startTime + duration);
    }

    playMetalClick(time, volume = 1) {
        if (!this.audioContext) return;

        const bufferSize = this.audioContext.sampleRate * 0.03;
        const buffer = this.audioContext.createBuffer(1, bufferSize, this.audioContext.sampleRate);
        const output = buffer.getChannelData(0);

        for (let i = 0; i < bufferSize; i++) {
            output[i] = (Math.random() * 2 - 1) * Math.pow(1 - i / bufferSize, 2);
        }

        const noise = this.audioContext.createBufferSource();
        noise.buffer = buffer;

        const filter = this.audioContext.createBiquadFilter();
        filter.type = 'highpass';
        filter.frequency.setValueAtTime(1500, time);

        const gain = this.audioContext.createGain();
        gain.gain.setValueAtTime(0.2 * volume, time);
        gain.gain.exponentialRampToValueAtTime(0.001, time + 0.1);

        noise.connect(filter).connect(gain).connect(this.audioContext.destination);
        noise.start(time);
        noise.stop(time + 0.1);
    }

    playWobbleSpin(startTime, duration) {
        if (!this.audioContext) return;

        const osc = this.audioContext.createOscillator();
        const gain = this.audioContext.createGain();

        osc.type = 'sine';
        osc.frequency.setValueAtTime(30, startTime);
        osc.frequency.exponentialRampToValueAtTime(8, startTime + duration); // slow wobble

        gain.gain.setValueAtTime(0.08, startTime);
        gain.gain.exponentialRampToValueAtTime(0.001, startTime + duration);

        osc.connect(gain).connect(this.audioContext.destination);
        osc.start(startTime);
        osc.stop(startTime + duration);
    }

    // เสียงชนะ (ascending chime)
    generateWinSound() {
        if (!this.audioContext) return;

        const frequencies = [523.25, 659.25, 783.99, 1046.50]; // C5, E5, G5, C6
        
        frequencies.forEach((freq, index) => {
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(this.audioContext.destination);
            
            oscillator.frequency.setValueAtTime(freq, this.audioContext.currentTime + index * 0.15);
            oscillator.type = 'sine';
            
            gainNode.gain.setValueAtTime(0, this.audioContext.currentTime + index * 0.15);
            gainNode.gain.linearRampToValueAtTime(0.2, this.audioContext.currentTime + index * 0.15 + 0.05);
            gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + index * 0.15 + 0.4);
            
            oscillator.start(this.audioContext.currentTime + index * 0.15);
            oscillator.stop(this.audioContext.currentTime + index * 0.15 + 0.4);
        });
    }

    // เสียงแพ้ (descending tone)
    generateLoseSound() {
        if (!this.audioContext) return;

        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);
        
        oscillator.frequency.setValueAtTime(400, this.audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(200, this.audioContext.currentTime + 0.5);
        oscillator.type = 'sawtooth';
        
        gainNode.gain.setValueAtTime(0.2, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.5);
        
        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + 0.5);
    }
}

/**
 * CoinRenderer Class - จัดการ 3D animation ด้วย Three.js
 */
class CoinRenderer {
    /**
     * Constructor สำหรับ CoinRenderer
     * @param {string|HTMLElement} canvasId - ID ของ canvas element หรือ element โดยตรง
     * @param {Object} options - ตัวเลือกการตั้งค่า
     */
    constructor(canvasId, options = {}) {
        // ตรวจสอบว่า Three.js พร้อมใช้งาน
        if (!THREE) {
            throw new Error('Three.js is required but not found. Please include Three.js before using CoinFlipper.');
        }

        // === ตั้งค่าพื้นฐาน ===
        this.canvas = typeof canvasId === 'string' ? document.getElementById(canvasId) : canvasId;
        this.options = {
            idleSpeed: 0.02,        // ความเร็วการหมุนใน idle mode
            flipDuration: 2000,     // ระยะเวลาการโยน (มิลลิวินาที)
            ...options
        };

        // === สถานะการทำงาน ===
        this.isFlipping = false;            // กำลังโยนเหรียญอยู่หรือไม่
        this.isIdle = false;                // อยู่ใน idle mode หรือไม่
        this.isShowingFinishScene = false;  // กำลังแสดง finish scene หรือไม่
        this.animationId = null;            // ID ของ animation frame

        // === สถานะแอนิเมชัน ===
        this.coinRotationX = 0;             // การหมุนรอบแกน X
        this.coinRotationY = 0;             // การหมุนรอบแกน Y
        this.coinRotationZ = 0;             // การหมุนรอบแกน Z
        this.coinPositionY = 0;             // ตำแหน่ง Y ของเหรียญ
        this.velocity = { x: 0, y: 0, z: 0 };           // ความเร็วในแต่ละแกน
        this.angularVelocity = { x: 0, y: 0, z: 0 };    // ความเร็วเชิงมุมในแต่ละแกน

        // === ค่าคงที่ Physics ===
        this.gravity = -0.015;              // แรงโน้มถ่วง
        this.bounceCount = 0;               // จำนวนครั้งที่กระเด้ง
        this.maxBounces = 3;                // จำนวนการกระเด้งสูงสุด
        this.bounceDamping = 0.6;           // การลดพลังงานเมื่อกระเด้ง
        this.rotationDamping = 0.98;        // การลดความเร็วการหมุน

        // === สถานะ Finish Scene Animation ===
        this.finishSceneProgress = 0;           // ความคืบหน้าของ finish scene (0-1)
        this.finishSceneDuration = 2000;        // ระยะเวลา finish scene (2 วินาที)
        this.finishSceneStartTime = 0;          // เวลาเริ่มต้น finish scene
        this.finishScenePauseTime = 500;        // เวลาหยุดพักที่ตำแหน่งสุดท้าย (0.5 วินาที)
        this.finishScenePhase = 'pause';        // เฟสปัจจุบัน: 'pause' หรือ 'animate'

        // === เริ่มต้นระบบ ===
        this.initThreeJS();         // สร้าง Three.js scene
        this.createCoin();          // สร้างเหรียญ 3D
        this.setupLighting();       // ตั้งค่าแสง
        this.startRenderLoop();     // เริ่ม render loop
    }

    initThreeJS() {
        console.log('🎬 Initializing Three.js...');
        console.log('Canvas element:', this.canvas);

        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x222222);

        // Get canvas dimensions
        const width = this.canvas.width || this.canvas.clientWidth || 400;
        const height = this.canvas.height || this.canvas.clientHeight || 400;
        console.log('Canvas dimensions:', width, 'x', height);

        this.camera = new THREE.PerspectiveCamera(
            75,
            width / height,
            0.1,
            1000
        );
        this.camera.position.set(0, 0, 3);
        console.log('Camera position:', this.camera.position);

        this.renderer = new THREE.WebGLRenderer({
            canvas: this.canvas,
            alpha: true,
            antialias: true
        });
        this.renderer.setSize(width, height);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

        console.log('✅ Three.js initialized');
    }

    createCoin() {
        console.log('🪙 Creating coin...');
        const geometry = new THREE.CylinderGeometry(0.8, 0.8, 0.1, 32);

        // Create textures for heads and tails
        const headsTexture = this.createHeadsTexture();
        const tailsTexture = this.createTailsTexture();
        const edgeTexture = this.createEdgeTexture();

        // Materials array for different faces
        const materials = [
            new THREE.MeshPhongMaterial({ map: edgeTexture }), // Side
            new THREE.MeshPhongMaterial({ map: headsTexture }), // Top (Heads)
            new THREE.MeshPhongMaterial({ map: tailsTexture })  // Bottom (Tails)
        ];

        this.coin = new THREE.Mesh(geometry, materials);
        this.coin.castShadow = true;
        this.coin.receiveShadow = true;

        // Set initial position
        this.coin.position.set(0, 0, 0);
        this.coin.rotation.set(0, 0, 0);

        this.scene.add(this.coin);

        console.log('✅ Coin created and added to scene');
        console.log('Coin position:', this.coin.position);
        console.log('Scene children count:', this.scene.children.length);
    }

    createHeadsTexture() {
        const canvas = document.createElement('canvas');
        canvas.width = 256;
        canvas.height = 256;
        const ctx = canvas.getContext('2d');

        // Gold gradient background
        const gradient = ctx.createRadialGradient(128, 128, 0, 128, 128, 128);
        gradient.addColorStop(0, '#FFD700');
        gradient.addColorStop(0.7, '#FFA500');
        gradient.addColorStop(1, '#B8860B');

        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 256, 256);

        // Border
        ctx.strokeStyle = '#8B4513';
        ctx.lineWidth = 8;
        ctx.beginPath();
        ctx.arc(128, 128, 120, 0, Math.PI * 2);
        ctx.stroke();

        // Create a separate canvas for image processing to avoid CORS issues
        const imageCanvas = document.createElement('canvas');
        imageCanvas.width = 256;
        imageCanvas.height = 256;
        const imageCtx = imageCanvas.getContext('2d');

        // Load and draw the H.jpeg image
        const img = new Image();
        img.crossOrigin = 'anonymous'; // Enable CORS

        img.onload = () => {
            // Calculate dimensions to fit the image within the circular border
            const borderRadius = 120;
            const borderDiameter = borderRadius * 2;
            const padding = 20; // Add some padding from the border
            const availableSize = borderDiameter - (padding * 2);

            // Calculate scaling to fit image within available space while maintaining aspect ratio
            const imgAspectRatio = img.width / img.height;
            let drawWidth, drawHeight;

            if (imgAspectRatio > 1) {
                // Image is wider than tall
                drawWidth = availableSize;
                drawHeight = availableSize / imgAspectRatio;
            } else {
                // Image is taller than wide or square
                drawHeight = availableSize;
                drawWidth = availableSize * imgAspectRatio;
            }

            // Center the image
            const drawX = (imageCanvas.width - drawWidth) / 2;
            const drawY = (imageCanvas.height - drawHeight) / 2;

            // Draw image to separate canvas first
            imageCtx.drawImage(img, drawX, drawY, drawWidth, drawHeight);

            // Create circular clipping path on main canvas
            ctx.save();
            ctx.beginPath();
            ctx.arc(128, 128, borderRadius - 10, 0, Math.PI * 2);
            ctx.clip();

            // Draw the processed image from imageCanvas to main canvas
            ctx.drawImage(imageCanvas, 0, 0);

            ctx.restore();

            // Update the texture after image is loaded
            if (this.coin && this.coin.material && this.coin.material[1]) {
                this.coin.material[1].map.needsUpdate = true;
            }
        };

        img.onerror = () => {
            console.error('Failed to load H.jpeg image, falling back to text');
            // Fallback to original text rendering if image fails to load
            ctx.fillStyle = '#8B4513';
            ctx.font = 'bold 120px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('หัว', 128, 128);

            // Update the texture after fallback rendering
            if (this.coin && this.coin.material && this.coin.material[1]) {
                this.coin.material[1].map.needsUpdate = true;
            }
        };

        //HEADS : Set the image source
        img.src = 'images/h_image.png';
        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.ClampToEdgeWrapping;
        texture.wrapT = THREE.ClampToEdgeWrapping;
        return texture;
    }

    createTailsTexture() {
        const canvas = document.createElement('canvas');
        canvas.width = 256;
        canvas.height = 256;
        const ctx = canvas.getContext('2d');

        // Silver gradient background
        const gradient = ctx.createRadialGradient(128, 128, 0, 128, 128, 128);
        gradient.addColorStop(0, '#FFD700');
        gradient.addColorStop(0.7, '#FFA500');
        gradient.addColorStop(1, '#B8860B');

        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 256, 256);

        // Border
        ctx.strokeStyle = '#8B4513';
        ctx.lineWidth = 8;
        ctx.beginPath();
        ctx.arc(128, 128, 120, 0, Math.PI * 2);
        ctx.stroke();

        // Create a separate canvas for image processing to avoid CORS issues
        const imageCanvas = document.createElement('canvas');
        imageCanvas.width = 256;
        imageCanvas.height = 256;
        const imageCtx = imageCanvas.getContext('2d');

        // Load and draw the coin face image
        const img = new Image();
        img.crossOrigin = 'anonymous'; // Enable CORS

        img.onload = () => {
            // Calculate dimensions to fit the image within the circular border
            const borderRadius = 120;
            const borderDiameter = borderRadius * 2;
            const padding = 20; // Add some padding from the border
            const availableSize = borderDiameter - (padding * 2);

            // Calculate scaling to fit image within available space while maintaining aspect ratio
            const imgAspectRatio = img.width / img.height;
            let drawWidth, drawHeight;

            if (imgAspectRatio > 1) {
                // Image is wider than tall
                drawWidth = availableSize;
                drawHeight = availableSize / imgAspectRatio;
            } else {
                // Image is taller than wide or square
                drawHeight = availableSize;
                drawWidth = availableSize * imgAspectRatio;
            }

            // Center the image
            const drawX = (imageCanvas.width - drawWidth) / 2;
            const drawY = (imageCanvas.height - drawHeight) / 2;

            // Draw image to separate canvas first
            imageCtx.drawImage(img, drawX, drawY, drawWidth, drawHeight);

            // Create circular clipping path on main canvas
            ctx.save();
            ctx.beginPath();
            ctx.arc(128, 128, borderRadius - 10, 0, Math.PI * 2);
            ctx.clip();

            // Draw the processed image from imageCanvas to main canvas
            ctx.drawImage(imageCanvas, 0, 0);

            ctx.restore();

            // Update the texture after image is loaded
            if (this.coin && this.coin.material && this.coin.material[2]) {
                this.coin.material[2].map.needsUpdate = true;
            }
        };

        img.onerror = () => {
            console.error('Failed to load T.png image, falling back to text');
            // Fallback to original text rendering if image fails to load
            ctx.fillStyle = '#2C2C2C';
            ctx.font = 'bold 120px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('ก้อย', 128, 128);

            // Update the texture after fallback rendering
            if (this.coin && this.coin.material && this.coin.material[2]) {
                this.coin.material[2].map.needsUpdate = true;
            }
        };

        // TAILS: Set the image sources
        img.src = 'images/t_image.png';
        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.ClampToEdgeWrapping;
        texture.wrapT = THREE.ClampToEdgeWrapping;
        return texture;
    }

    createEdgeTexture() {
        const canvas = document.createElement('canvas');
        canvas.width = 256;
        canvas.height = 32;
        const ctx = canvas.getContext('2d');

        // Gold edge color
        const gradient = ctx.createLinearGradient(0, 0, 0, 32);
        gradient.addColorStop(0, '#FFD700');
        gradient.addColorStop(0.5, '#FFA500');
        gradient.addColorStop(1, '#FFD700');

        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 256, 32);

        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.ClampToEdgeWrapping;
        return texture;
    }

    setupLighting() {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);

        // Directional light (main light)
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(2, 4, 2);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 1024;
        directionalLight.shadow.mapSize.height = 1024;
        this.scene.add(directionalLight);

        // Point light for extra shine
        const pointLight = new THREE.PointLight(0xffd700, 0.3, 10);
        pointLight.position.set(-2, 2, 2);
        this.scene.add(pointLight);
    }

    startRenderLoop() {
        console.log('✅ Starting render loop');
        const animate = () => {
            this.animationId = requestAnimationFrame(animate);
            this.updateAnimation();
            this.renderer.render(this.scene, this.camera);
        };
        animate();
    }

    /**
     * อัพเดทแอนิเมชันหลัก - เรียกในทุกเฟรมเพื่อจัดการแอนิเมชันต่างๆ
     * ตัดสินใจว่าจะเรียกแอนิเมชันแบบไหนตามสถานะปัจจุบัน
     */
    updateAnimation() {
        if (this.isIdle && !this.isFlipping && !this.isShowingFinishScene) {
            // === Idle Animation ===
            // หมุนเหรียญเบาๆ รอบแกน X เมื่ออยู่ในสถานะพัก
            this.coin.rotation.x += this.options.idleSpeed;
        } else if (this.isFlipping) {
            // === Flip Animation ===
            // แอนิเมชันการโยนเหรียญ - จำลอง physics และการกระเด้ง
            this.updateFlipAnimation();
        } else if (this.isShowingFinishScene) {
            // === Finish Scene Animation ===
            // แอนิเมชันแสดงผลสุดท้าย - เคลื่อนไหวไปยังตำแหน่งแสดงผล
            this.updateFinishScene();
        }
    }

    /**
     * อัพเดทแอนิเมชันการโยนเหรียญ - จำลอง physics การเคลื่อนไหวและการหมุน
     */
    updateFlipAnimation() {
        // === การจำลอง Physics ===
        // ปรับความเร็วในแนวตั้ง (Y) ด้วยแรงโน้มถ่วง
        this.velocity.y += this.gravity;
        // อัพเดทตำแหน่ง Y ของเหรียญตามความเร็ว
        this.coinPositionY += this.velocity.y;

        // === การหมุน (Rotation) ===
        // อัพเดทการหมุนในแต่ละแกนตามความเร็วเชิงมุม
        this.coinRotationX += this.angularVelocity.x; // การหมุนแบบ end-over-end (หัว-ก้อย)
        this.coinRotationY += this.angularVelocity.y; // การหมุนรอบแกนตั้ง
        this.coinRotationZ += this.angularVelocity.z; // การหมุนแบบ wobble

        // === การลดความเร็วการหมุน (Damping) ===
        // ลดความเร็วการหมุนเล็กน้อยในแต่ละเฟรมเพื่อจำลองแรงต้าน
        this.angularVelocity.x *= this.rotationDamping;
        this.angularVelocity.y *= this.rotationDamping;
        this.angularVelocity.z *= this.rotationDamping;

        // === การกระเด้ง (Bouncing) ===
        // ตรวจสอบว่าเหรียญกระทบพื้น (Y <= -1.5) และกำลังตกลงมา (velocity.y < 0)
        if (this.coinPositionY <= -1.5 && this.velocity.y < 0) {
            // ตั้งตำแหน่งเหรียญให้อยู่บนพื้นพอดี
            this.coinPositionY = -1.5;
            // กลับทิศความเร็วและลดลงตาม bounceDamping (การสูญเสียพลังงานจากการกระเด้ง)
            this.velocity.y = -this.velocity.y * this.bounceDamping;
            // นับจำนวนครั้งที่กระเด้ง
            this.bounceCount++;

            // เพิ่มการหมุนแบบสุ่มเล็กน้อยเมื่อกระเด้งเพื่อความสมจริง
            this.angularVelocity.x += (Math.random() - 0.5) * 0.1;
            this.angularVelocity.z += (Math.random() - 0.5) * 0.1;

            // หยุดการกระเด้งเมื่อกระเด้งครบจำนวนและความเร็วต่ำพอ
            if (this.bounceCount >= this.maxBounces && Math.abs(this.velocity.y) < 0.05) {
                this.velocity.y = 0; // หยุดการเคลื่อนไหวในแนวตั้ง
                this.coinPositionY = -1.5; // ตั้งตำแหน่งสุดท้ายบนพื้น
                this.stopFlipping(); // เริ่ม finish scene
            }
        }

        // === ปรับค่าการแปลงให้กับเหรียญ 3D ===
        // อัพเดทตำแหน่งและการหมุนของเหรียญใน Three.js scene
        this.coin.position.y = this.coinPositionY;
        this.coin.rotation.x = this.coinRotationX;
        this.coin.rotation.y = this.coinRotationY;
        this.coin.rotation.z = this.coinRotationZ;
    }

    /**
     * เริ่ม idle animation - แอนิเมชันหมุนเบาๆ เมื่อไม่ได้ใช้งาน
     */
    startIdle() {
        console.log('✅ Starting idle animation');

        // รีเซ็ตเหรียญให้ตั้งขึ้นก่อนเริ่มหมุน
        this.resetCoinToVertical();

        // ตั้งค่าสถานะ idle
        this.isIdle = true;
        this.isFlipping = false;
    }

    /**
     * รีเซ็ตเหรียญให้อยู่ในตำแหน่งตั้งขึ้น (vertical position)
     * ใช้สำหรับเตรียมเหรียญก่อนเริ่ม idle animation
     */
    resetCoinToVertical() {
        // === รีเซ็ตกล้องกลับสู่ตำแหน่งเริ่มต้น ===
        this.camera.position.set(0, 0, 3);
        this.camera.lookAt(0, 0, 0);

        // === รีเซ็ตเหรียญให้ตั้งขึ้น (ยืนบนขอบ) ===
        this.coin.scale.set(1, 1, 1);                    // ขนาดปกติ
        this.coin.position.set(0, 0, 0);                 // ตำแหน่งกึ่งกลาง
        this.coin.rotation.set(Math.PI / 2, 0, 0);       // หมุน 90 องศารอบแกน X เพื่อให้ตั้งขึ้น

        // === รีเซ็ตสถานะภายในให้ตรงกับตำแหน่งตั้งขึ้น ===
        this.coinRotationX = Math.PI / 2;                // 90 องศาในหน่วย radian
        this.coinRotationY = 0;                          // ไม่หมุนรอบแกน Y
        this.coinRotationZ = 0;                          // ไม่หมุนรอบแกน Z
        this.coinPositionY = 0;                          // ตำแหน่งกึ่งกลาง
        this.velocity = { x: 0, y: 0, z: 0 };           // ไม่มีความเร็ว
        this.angularVelocity = { x: 0, y: 0, z: 0 };    // ไม่มีความเร็วเชิงมุม
        this.bounceCount = 0;                            // รีเซ็ตจำนวนการกระเด้ง

        // === รีเซ็ตความเข้มของแสง ambient ===
        if (this.scene.children[0] && this.scene.children[0].isAmbientLight) {
            this.scene.children[0].intensity = 0.6;
        }

        console.log('🔄 Coin reset to vertical position for idle animation');
    }

    /**
     * หยุด idle animation
     */
    stopIdle() {
        this.isIdle = false;
    }

    /**
     * ทอยเหรียญ - เมธอดหลักสำหรับเริ่มแอนิเมชันการโยนเหรียญ
     * @param {string|null} result - ผลลัพธ์ที่ต้องการ ('heads', 'tails', หรือ null สำหรับสุ่ม)
     * @returns {Promise<string>} - Promise ที่ resolve เมื่อการโยนเสร็จสิ้น พร้อมผลลัพธ์
     */
    async flipCoin(result = null) {
        // ตรวจสอบว่าไม่ได้อยู่ในระหว่างการโยนหรือแสดงผลสุดท้ายอยู่
        if (this.isFlipping || this.isShowingFinishScene) return;

        // === ตั้งค่าสถานะการโยน ===
        this.isFlipping = true;           // เริ่มการโยน
        this.isIdle = false;              // หยุด idle animation
        this.isShowingFinishScene = false; // ยังไม่ใช่ช่วงแสดงผลสุดท้าย
        this.bounceCount = 0;             // รีเซ็ตจำนวนการกระเด้ง

        // เก็บผลลัพธ์ที่ต้องการไว้ใช้ใน finish scene
        this.intendedResult = typeof result === 'string' ? result.toLowerCase() : result;

        // === รีเซ็ตกล้องและเหรียญสำหรับการโยนใหม่ ===
        // รีเซ็ตตำแหน่งกล้องกลับสู่ตำแหน่งเริ่มต้น
        this.camera.position.set(0, 0, 3);
        this.camera.lookAt(0, 0, 0);

        // รีเซ็ตขนาดเหรียญกลับสู่ขนาดเริ่มต้น
        this.coin.scale.set(1, 1, 1);

        // === รีเซ็ตตำแหน่งและการหมุนสำหรับการโยนใหม่ ===
        this.coinPositionY = 0;  // ตำแหน่งเริ่มต้นที่กึ่งกลาง
        this.coinRotationX = 0;  // ไม่มีการหมุนรอบแกน X
        this.coinRotationY = 0;  // ไม่มีการหมุนรอบแกน Y
        this.coinRotationZ = 0;  // ไม่มีการหมุนรอบแกน Z

        // === ตั้งค่าความเร็วเริ่มต้นสำหรับการโยนที่สมจริง ===
        this.velocity.y = 0.3; // ความเร็วขึ้นในแนวตั้ง (โยนขึ้น)

        // === คำนวณการหมุนเป้าหมายตามผลลัพธ์ที่ต้องการ ===
        const baseRotations = 4; // จำนวนรอบการหมุนพื้นฐาน
        // คำนวณการหมุนรอบแกน X (end-over-end flip แบบธรรมชาติ)
        const targetRotationX = result === 'heads'
            ? baseRotations * Math.PI * 2                    // หัว: หมุนเป็นจำนวนรอบเต็ม
            : (baseRotations * Math.PI * 2) + Math.PI;       // ก้อย: หมุนเป็นจำนวนรอบเต็ม + ครึ่งรอบ

        // === ตั้งค่าความเร็วเชิงมุม (Angular Velocity) ===
        this.angularVelocity.x = targetRotationX / 120;      // กระจายการหมุนตลอด ~2 วินาที (120 เฟรม)
        this.angularVelocity.y = (Math.random() - 0.5) * 0.1; // การหมุนข้างแบบสุ่มเล็กน้อย
        this.angularVelocity.z = (Math.random() - 0.5) * 0.1; // การโซ่แบบสุ่มเล็กน้อย

        // === คืนค่า Promise ที่จะ resolve เมื่อแอนิเมชันเสร็จสิ้น ===
        return new Promise((resolve) => {
            this.flipResolve = resolve; // เก็บ resolve function ไว้เรียกใน finish scene
        });
    }

    /**
     * หยุดการโยนเหรียญและเริ่ม Finish Scene
     * เรียกเมื่อเหรียญหยุดกระเด้งและพร้อมแสดงผลสุดท้าย
     */
    stopFlipping() {
        console.log('🔄 Stopping flip animation at position:', {
            x: this.coinRotationX,
            y: this.coinRotationY,
            z: this.coinRotationZ,
            posY: this.coinPositionY
        });

        // เปลี่ยนสถานะจากการโยนเป็นการแสดงผลสุดท้าย
        this.isFlipping = false;

        // เริ่มแอนิเมชัน finish scene
        this.startFinishScene();
    }

    /**
     * เริ่มต้น Finish Scene - ช่วงแสดงผลสุดท้ายหลังจากเหรียญหยุดกระเด้ง
     * จัดเตรียมข้อมูลสำหรับแอนิเมชันการเปลี่ยนจากตำแหน่งสุดท้ายของการโยนไปยังตำแหน่งแสดงผล
     */
    startFinishScene() {
        console.log('🎬 Starting finish scene with pause phase');

        // === ตั้งค่าสถานะ Finish Scene ===
        this.isShowingFinishScene = true;           // เริ่ม finish scene
        this.finishSceneStartTime = Date.now();     // บันทึกเวลาเริ่มต้น
        this.finishSceneProgress = 0;               // รีเซ็ต progress
        this.finishScenePhase = 'pause';            // เริ่มด้วยเฟส 'pause'

        // === กำหนดผลลัพธ์สุดท้าย ===
        // ใช้ผลลัพธ์ที่กำหนดไว้ตั้งแต่เริ่มโยน หรือคำนวณจากตำแหน่งปัจจุบัน
        const finalResult = this.intendedResult || this.getCurrentResult();

        // === เก็บสถานะปัจจุบันเป็นตำแหน่งสุดท้ายของการโยน ===
        // (จะไม่เปลี่ยนแปลงในช่วง pause)
        this.finishSceneFinalFlipRotation = {
            x: this.coinRotationX,
            y: this.coinRotationY,
            z: this.coinRotationZ
        };
        this.finishSceneFinalFlipPosition = this.coinPositionY;

        // === คำนวณการหมุนเป้าหมายเพื่อแสดงหน้าที่ชนะอย่างชัดเจน ===
        // ตั้งค่าการหมุนรอบแกน X: 0 สำหรับหัว, π สำหรับก้อย
        const targetX = finalResult === 'heads' ? 0 : Math.PI;

        // === หาเส้นทางการหมุนที่สั้นที่สุด ===
        // ทำให้การหมุน X ปัจจุบันอยู่ในช่วง 0 ถึง 2π
        const currentX = ((this.coinRotationX % (Math.PI * 2)) + (Math.PI * 2)) % (Math.PI * 2);
        let deltaX = targetX - currentX;

        // เลือกเส้นทางการหมุนที่สั้นที่สุด (ไม่เกิน 180 องศา)
        if (deltaX > Math.PI) {
            deltaX -= Math.PI * 2;      // หมุนทางซ้ายแทน
        } else if (deltaX < -Math.PI) {
            deltaX += Math.PI * 2;      // หมุนทางขวาแทน
        }

        // === ตั้งค่าการหมุนเป้าหมายสำหรับการแสดงผลแนวตั้ง ===
        this.finishSceneTargetRotation = {
            x: currentX + deltaX,       // การหมุนที่คำนวณแล้ว
            y: 0,                       // ไม่หมุนรอบแกน Y
            z: 0                        // ไม่หมุนรอบแกน Z
        };

        // === ตั้งค่าตำแหน่งเป้าหมาย ===
        // ยกเหรียญขึ้นเล็กน้อยและจัดกึ่งกลางเพื่อการมองเห็นที่ชัดเจน
        this.finishSceneTargetPosition = -0.3;

        // === เก็บข้อมูลแอนิเมชันกล้อง ===
        this.finishSceneStartCameraPosition = this.camera.position.clone();           // ตำแหน่งเริ่มต้น
        this.finishSceneTargetCameraPosition = new THREE.Vector3(0, 1.5, 2.5);       // ตำแหน่งเป้าหมาย (มองจากด้านบน)

        // === เก็บข้อมูลแอนิเมชันขนาดเหรียญ ===
        this.finishSceneStartScale = this.coin.scale.clone();                        // ขนาดเริ่มต้น
        this.finishSceneTargetScale = new THREE.Vector3(1.2, 1.2, 1.2);             // ขนาดเป้าหมาย (ขยายเล็กน้อย)
    }

    /**
     * อัพเดทแอนิเมชันในช่วงสุดท้ายของการโยนเหรียญ (Finish Scene)
     * แบ่งเป็น 2 เฟส: 'pause' (หยุดชั่วคราว) และ 'animate' (เคลื่อนไหวไปตำแหน่งสุดท้าย)
     */
    updateFinishScene() {
        // คำนวณเวลาที่ผ่านไปตั้งแต่เริ่ม finish scene
        const elapsed = Date.now() - this.finishSceneStartTime;

        // === เฟส 'pause': หยุดเหรียญในตำแหน่งสุดท้ายของการโยนชั่วคราว ===
        if (this.finishScenePhase === 'pause') {
            // รักษาตำแหน่งและการหมุนของเหรียญให้คงที่ตามผลการโยนสุดท้าย
            this.coinRotationX = this.finishSceneFinalFlipRotation.x;
            this.coinRotationY = this.finishSceneFinalFlipRotation.y;
            this.coinRotationZ = this.finishSceneFinalFlipRotation.z;
            this.coinPositionY = this.finishSceneFinalFlipPosition;

            // ปรับค่าการแปลงให้กับเหรียญ (ไม่มีการเปลี่ยนแปลงในช่วง pause)
            this.coin.rotation.set(this.coinRotationX, this.coinRotationY, this.coinRotationZ);
            this.coin.position.y = this.coinPositionY;

            // ตรวจสอบว่าเวลา pause หมดแล้วหรือยัง
            if (elapsed >= this.finishScenePauseTime) {
                console.log('🎬 Switching to animate phase after pause');
                // เปลี่ยนไปเฟส 'animate'
                this.finishScenePhase = 'animate';
                this.finishSceneAnimateStartTime = Date.now();

                // ตั้งค่าจุดเริ่มต้นของแอนิเมชันเป็นตำแหน่งสุดท้ายของการโยน
                this.finishSceneStartRotation = {
                    x: this.finishSceneFinalFlipRotation.x,
                    y: this.finishSceneFinalFlipRotation.y,
                    z: this.finishSceneFinalFlipRotation.z
                };
                this.finishSceneStartPosition = this.finishSceneFinalFlipPosition;
            }
            return; // ออกจากฟังก์ชันในช่วง pause
        }

        // === เฟส 'animate': เคลื่อนไหวเรียบๆ ไปยังตำแหน่งแสดงผลสุดท้าย ===
        const animateElapsed = Date.now() - this.finishSceneAnimateStartTime;
        this.finishSceneProgress = Math.min(animateElapsed / this.finishSceneDuration, 1);

        // ใช้ easing function เพื่อให้แอนิเมชันเรียบเนียน
        const easeProgress = this.easeInOutCubic(this.finishSceneProgress);

        // === การหมุน (Rotation): ทำ interpolation จากตำแหน่งปัจจุบันไปยังตำแหน่งเป้าหมาย ===
        this.coinRotationX = this.lerp(
            this.finishSceneStartRotation.x,
            this.finishSceneTargetRotation.x,
            easeProgress
        );
        this.coinRotationY = this.lerp(
            this.finishSceneStartRotation.y,
            this.finishSceneTargetRotation.y,
            easeProgress
        );
        this.coinRotationZ = this.lerp(
            this.finishSceneStartRotation.z,
            this.finishSceneTargetRotation.z,
            easeProgress
        );

        // === ตำแหน่ง (Position): ทำ interpolation ตำแหน่ง Y ของเหรียญ ===
        this.coinPositionY = this.lerp(
            this.finishSceneStartPosition,
            this.finishSceneTargetPosition,
            easeProgress
        );

        // === กล้อง (Camera): ปรับตำแหน่งกล้องเพื่อมองเหรียญในมุมที่เหมาะสม ===
        this.camera.position.lerpVectors(
            this.finishSceneStartCameraPosition,
            this.finishSceneTargetCameraPosition,
            easeProgress * 0.6 // ใช้ค่า 0.6 เพื่อให้กล้องเคลื่อนไหวช้ากว่าเหรียญเล็กน้อย
        );

        // ให้กล้องมองลงมาที่เหรียญเพื่อเห็นผลลัพธ์ชัดเจน
        this.camera.lookAt(0, this.coinPositionY, 0);

        // === ขนาด (Scale): ขยายเหรียญเล็กน้อยเพื่อเน้นผลลัพธ์ ===
        this.coin.scale.lerpVectors(
            this.finishSceneStartScale,
            this.finishSceneTargetScale,
            easeProgress
        );

        // ปรับค่าการแปลงให้กับเหรียญ
        this.coin.rotation.set(this.coinRotationX, this.coinRotationY, this.coinRotationZ);
        this.coin.position.y = this.coinPositionY;

        // === เอฟเฟกต์พิเศษ: เพิ่มการลอยตัวและเรืองแสงเมื่อใกล้จบ ===
        if (this.finishSceneProgress >= 0.8) {
            // เอฟเฟกต์ลอยตัว: ใช้ sin wave เพื่อสร้างการเคลื่อนไหวขึ้นลงเล็กน้อย
            const totalElapsed = Date.now() - this.finishSceneStartTime;
            const floatOffset = Math.sin(totalElapsed * 0.003) * 0.02;
            this.coin.position.y += floatOffset;

            // เอฟเฟกต์เรืองแสง: ปรับความเข้มของแสง ambient เพื่อสร้างการเรืองแสง
            const glowIntensity = 0.6 + Math.sin(totalElapsed * 0.005) * 0.1;
            if (this.scene.children[0] && this.scene.children[0].isAmbientLight) {
                this.scene.children[0].intensity = glowIntensity;
            }
        }

        // === จบแอนิเมชัน: เมื่อ progress ถึง 100% ===
        if (this.finishSceneProgress >= 1) {
            this.isShowingFinishScene = false;

            // รีเซ็ตความเข้มของแสงกลับสู่ปกติ
            if (this.scene.children[0] && this.scene.children[0].isAmbientLight) {
                this.scene.children[0].intensity = 0.6;
            }

            // ตั้งค่าตำแหน่งและการหมุนสุดท้ายของเหรียญให้แน่นอน
            this.coin.rotation.set(
                this.finishSceneTargetRotation.x,
                this.finishSceneTargetRotation.y,
                this.finishSceneTargetRotation.z
            );
            this.coin.position.y = this.finishSceneTargetPosition;
            this.coin.scale.copy(this.finishSceneTargetScale);

            // อัพเดทค่าการติดตามการหมุนภายในให้ตรงกับสถานะสุดท้าย
            this.coinRotationX = this.finishSceneTargetRotation.x;
            this.coinRotationY = this.finishSceneTargetRotation.y;
            this.coinRotationZ = this.finishSceneTargetRotation.z;
            this.coinPositionY = this.finishSceneTargetPosition;

            // ส่งผลลัพธ์การโยนเหรียญกลับไปยัง Promise ที่รอผลลัพธ์อยู่
            if (this.flipResolve) {
                this.flipResolve(this.intendedResult || this.getCurrentResult());
                this.flipResolve = null;
            }
        }
    }

    /**
     * Easing function สำหรับแอนิเมชันที่เรียบเนียน
     * ใช้ cubic easing (เริ่มช้า -> เร็ว -> ช้า) เพื่อให้การเคลื่อนไหวดูธรรมชาติ
     * @param {number} t - ค่า progress ระหว่าง 0-1
     * @returns {number} - ค่าที่ผ่าน easing แล้ว
     */
    easeInOutCubic(t) {
        return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
    }

    /**
     * Linear interpolation helper - คำนวณค่าระหว่างจุดเริ่มต้นและจุดสิ้นสุด
     * @param {number} start - ค่าเริ่มต้น
     * @param {number} end - ค่าสิ้นสุด
     * @param {number} t - ตำแหน่งระหว่าง 0-1 (0=start, 1=end)
     * @returns {number} - ค่าที่คำนวณได้
     */
    lerp(start, end, t) {
        return start + (end - start) * t;
    }

    /**
     * ตรวจสอบผลลัพธ์ปัจจุบันของเหรียญ
     * วิเคราะห์จากการหมุนรอบแกน X เพื่อดูว่าหน้าไหนหันขึ้น
     * @returns {string} - 'heads' หรือ 'tails'
     */
    getCurrentResult() {
        // ทำให้การหมุน X อยู่ในช่วง 0 ถึง 2π
        const normalizedRotation = ((this.coinRotationX % (Math.PI * 2)) + (Math.PI * 2)) % (Math.PI * 2);
        // ถ้าการหมุนน้อยกว่า π (180°) = หัว, มากกว่า = ก้อย
        return normalizedRotation < Math.PI ? 'heads' : 'tails';
    }

    // ทำลาย renderer
    destroy() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        if (this.renderer) {
            this.renderer.dispose();
        }
    }

    // Method to reset coin to initial state (useful for game reset)
    resetCoin() {
        this.isFlipping = false;
        this.isShowingFinishScene = false;
        this.isIdle = false;

        // Reset camera to original position
        this.camera.position.set(0, 0, 3);
        this.camera.lookAt(0, 0, 0);

        // Reset coin to original state
        this.coin.scale.set(1, 1, 1);
        this.coin.position.set(0, 0, 0);
        this.coin.rotation.set(0, 0, 0);

        // Reset internal state
        this.coinRotationX = 0;
        this.coinRotationY = 0;
        this.coinRotationZ = 0;
        this.coinPositionY = 0;
        this.velocity = { x: 0, y: 0, z: 0 };
        this.angularVelocity = { x: 0, y: 0, z: 0 };
        this.bounceCount = 0;

        // Reset ambient light intensity
        if (this.scene.children[0] && this.scene.children[0].isAmbientLight) {
            this.scene.children[0].intensity = 0.6;
        }
    }

    // Pulse effect for coin interaction
    pulseEffect() {
        if (this.isFlipping || this.isShowingFinishScene) return;

        const originalScale = this.coin.scale.clone();
        const targetScale = originalScale.clone().multiplyScalar(1.1);

        // Animate scale up and down
        const duration = 300;
        const startTime = Date.now();

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);

            let scale;
            if (progress < 0.5) {
                // Scale up
                const t = progress * 2;
                scale = originalScale.clone().lerp(targetScale, t);
            } else {
                // Scale down
                const t = (progress - 0.5) * 2;
                scale = targetScale.clone().lerp(originalScale, t);
            }

            this.coin.scale.copy(scale);

            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                this.coin.scale.copy(originalScale);
            }
        };

        animate();
    }

    // Resize handler
    resize() {
        if (this.camera && this.renderer) {
            this.camera.aspect = this.canvas.clientWidth / this.canvas.clientHeight;
            this.camera.updateProjectionMatrix();
            this.renderer.setSize(this.canvas.clientWidth, this.canvas.clientHeight);
        }
    }
}

/**
 * CoinFlipper Main Class - คลาสหลักสำหรับใช้งานใน Vue.js
 */
class CoinFlipper {
    constructor(canvasId, options = {}) {
        this.options = {
            autoLoadThreeJS: true,
            threeJSCDN: 'https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js',
            idleSpeed: 0.02,
            flipDuration: 2000,
            enableSound: true,
            ...options
        };

        this.canvasId = canvasId;
        this.audioManager = null;
        this.coinRenderer = null;
        this.isInitialized = false;
        this.initPromise = null;

        // Auto-initialize if Three.js is available
        if (THREE || !this.options.autoLoadThreeJS) {
            this.init();
        } else {
            this.initPromise = this.loadThreeJS().then(() => this.init());
        }
    }

    // โหลด Three.js จาก CDN
    async loadThreeJS() {
        if (typeof window === 'undefined') return;

        return new Promise((resolve, reject) => {
            if (window.THREE) {
                THREE = window.THREE;
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = this.options.threeJSCDN;
            script.onload = () => {
                THREE = window.THREE;
                resolve();
            };
            script.onerror = () => reject(new Error('Failed to load Three.js'));
            document.head.appendChild(script);
        });
    }

    // เริ่มต้นระบบ
    async init() {
        if (this.isInitialized) return;

        try {
            // Initialize audio manager
            if (this.options.enableSound) {
                this.audioManager = new AudioManager();
            }

            // Initialize coin renderer
            this.coinRenderer = new CoinRenderer(this.canvasId, {
                idleSpeed: this.options.idleSpeed,
                flipDuration: this.options.flipDuration
            });

            this.isInitialized = true;
        } catch (error) {
            console.error('Failed to initialize CoinFlipper:', error);
            throw error;
        }
    }

    // รอให้ระบบพร้อมใช้งาน
    async ready() {
        if (this.initPromise) {
            await this.initPromise;
        }
        if (!this.isInitialized) {
            await this.init();
        }
    }

    // เริ่ม idle animation (เหรียญหมุนเบาๆ)
    async startIdle() {
        await this.ready();
        if (this.coinRenderer) {
            this.coinRenderer.startIdle();
        }
    }

    // หยุด idle animation
    async stopIdle() {
        await this.ready();
        if (this.coinRenderer) {
            this.coinRenderer.stopIdle();
        }
    }

    /**
     * ทอยเหรียญ - เมธอดหลักสำหรับการโยนเหรียญ
     * @param {string|null} result - ผลลัพธ์ที่ต้องการ ('heads', 'tails', หรือ null สำหรับสุ่ม)
     * @param {boolean} playSound - เล่นเสียงหรือไม่ (default: true)
     * @returns {Promise<string>} - Promise ที่ resolve เมื่อการโยนเสร็จสิ้น พร้อมผลลัพธ์
     */
    async toss(result = null, playSound = true) {
        // รอให้ระบบพร้อมใช้งาน
        await this.ready();

        // ตรวจสอบว่า CoinRenderer ถูกสร้างแล้ว
        if (!this.coinRenderer) {
            throw new Error('CoinRenderer not initialized');
        }

        // === เล่นเสียงการทอย (ถ้าเปิดใช้งาน) ===
        if (playSound && this.audioManager) {
            this.audioManager.resumeAudioContext();    // เปิดใช้งาน AudioContext
            this.audioManager.generateFlipSound();     // เล่นเสียงการโยน
        }

        // === ทำการทอยและรอผลลัพธ์ ===
        const finalResult = await this.coinRenderer.flipCoin(result);

        return finalResult;
    }

    // เล่นเสียงชนะ
    async playWinSound() {
        await this.ready();
        if (this.audioManager) {
            this.audioManager.resumeAudioContext();
            this.audioManager.generateWinSound();
        }
    }

    // เล่นเสียงแพ้
    async playLoseSound() {
        await this.ready();
        if (this.audioManager) {
            this.audioManager.resumeAudioContext();
            this.audioManager.generateLoseSound();
        }
    }

    // รีเซ็ตเหรียญกลับสู่สถานะเริ่มต้น
    async resetCoin() {
        await this.ready();
        if (this.coinRenderer) {
            this.coinRenderer.resetCoin();
        }
    }

    // เอฟเฟกต์ pulse เมื่อคลิกเหรียญ
    async pulseEffect() {
        await this.ready();
        if (this.coinRenderer) {
            this.coinRenderer.pulseEffect();
        }
    }

    // ปรับขนาดตาม canvas
    async resize() {
        await this.ready();
        if (this.coinRenderer) {
            this.coinRenderer.resize();
        }
    }

    // ทำลายทุกอย่าง
    destroy() {
        if (this.coinRenderer) {
            this.coinRenderer.destroy();
            this.coinRenderer = null;
        }
        this.audioManager = null;
        this.isInitialized = false;
    }

    // ตรวจสอบสถานะ
    get status() {
        return {
            isInitialized: this.isInitialized,
            hasAudio: !!this.audioManager,
            hasRenderer: !!this.coinRenderer,
            isFlipping: this.coinRenderer ? this.coinRenderer.isFlipping : false,
            isIdle: this.coinRenderer ? this.coinRenderer.isIdle : false,
            isShowingFinishScene: this.coinRenderer ? this.coinRenderer.isShowingFinishScene : false
        };
    }
}

// Export สำหรับใช้ใน different environments
if (typeof module !== 'undefined' && module.exports) {
    // Node.js/CommonJS
    module.exports = { CoinFlipper, AudioManager, CoinRenderer };
} else if (typeof define === 'function' && define.amd) {
    // AMD
    define([], function() {
        return { CoinFlipper, AudioManager, CoinRenderer };
    });
} else {
    // Browser global
    window.CoinFlipper = CoinFlipper;
    window.CoinFlipperAudioManager = AudioManager;
    window.CoinFlipperRenderer = CoinRenderer;
}
